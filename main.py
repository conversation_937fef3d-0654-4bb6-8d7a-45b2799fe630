#!/usr/bin/env python3
"""
YOLO Object Detection Command-Line Application

This application provides object detection capabilities for fireworks and insects
using pre-trained YOLO models. It processes input images and outputs annotated
results with bounding boxes around detected objects.

Usage:
    python main.py --type fireworks --input image.jpg
    python main.py --type insects --input image.jpg --output result.jpg
"""

import argparse
import os
import sys
from pathlib import Path
import cv2

from ultralytics import YOLO


class YOLODetector:
    """YOLO Object Detection class for handling fireworks and insects detection."""

    def __init__(self):
        """Initialize the detector with model paths."""
        self.models = {
            'fireworks': 'fire_smoke.pt',
            'insects': 'bugs.pt'
        }
        self.current_model = None
        self.model_type = None

    def load_model(self, detection_type):
        """
        Load the appropriate YOLO model based on detection type.

        Args:
            detection_type (str): Type of detection ('fireworks' or 'insects')

        Returns:
            bool: True if model loaded successfully, False otherwise
        """
        if detection_type not in self.models:
            print(f"Error: Invalid detection type '{detection_type}'. "
                  f"Available types: {list(self.models.keys())}")
            return False

        model_path = self.models[detection_type]

        if not os.path.exists(model_path):
            print(f"Error: Model file '{model_path}' not found.")
            return False

        try:
            print(f"Loading {detection_type} detection model...")
            self.current_model = YOLO(model_path)
            self.model_type = detection_type
            print(f"Model loaded successfully: {model_path}")
            return True
        except Exception as e:
            error_msg = str(e)
            print(f"Error loading model '{model_path}': {error_msg}")

            # Provide helpful error messages for common issues
            if "Can't get attribute" in error_msg:
                print("This model appears to use custom modules that are not available.")
                print("Please ensure you have the correct ultralytics version or custom modules installed.")
            elif "No module named" in error_msg:
                print("Missing required dependencies. Please install the required packages.")
            elif "CUDA" in error_msg:
                print("CUDA-related error. The model will attempt to run on CPU.")

            return False

    def detect_objects(self, image_path, confidence_threshold=0.25):
        """
        Perform object detection on the input image.

        Args:
            image_path (str): Path to the input image
            confidence_threshold (float): Minimum confidence threshold for detections

        Returns:
            tuple: (results, image) or (None, None) if error
        """
        if self.current_model is None:
            print("Error: No model loaded. Please load a model first.")
            return None, None

        if not os.path.exists(image_path):
            print(f"Error: Input image '{image_path}' not found.")
            return None, None

        try:
            print(f"Processing image: {image_path}")

            # Load image
            image = cv2.imread(image_path)
            if image is None:
                print(f"Error: Could not load image '{image_path}'. "
                      "Please check if it's a valid image file.")
                return None, None

            # Run inference
            results = self.current_model(image_path, conf=confidence_threshold)

            return results, image

        except Exception as e:
            print(f"Error during detection: {str(e)}")
            return None, None

    def draw_detections(self, image, results):
        """
        Draw bounding boxes and labels on the image.

        Args:
            image (numpy.ndarray): Input image
            results: YOLO detection results

        Returns:
            tuple: (annotated_image, detection_count, detection_info)
        """
        annotated_image = image.copy()
        detection_count = 0
        detection_info = []

        # Colors for different classes (BGR format)
        colors = [
            (0, 255, 0),    # Green
            (255, 0, 0),    # Blue
            (0, 0, 255),    # Red
            (255, 255, 0),  # Cyan
            (255, 0, 255),  # Magenta
            (0, 255, 255),  # Yellow
        ]

        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # Get bounding box coordinates
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)

                    # Get confidence and class
                    confidence = box.conf[0].cpu().numpy()
                    class_id = int(box.cls[0].cpu().numpy())

                    # Get class name
                    class_name = self.current_model.names[class_id] if hasattr(self.current_model, 'names') else f"Class_{class_id}"

                    # Choose color
                    color = colors[class_id % len(colors)]

                    # Draw bounding box
                    cv2.rectangle(annotated_image, (x1, y1), (x2, y2), color, 2)

                    # Draw label with confidence
                    label = f"{class_name}: {confidence:.2f}"
                    label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

                    # Draw label background
                    cv2.rectangle(annotated_image,
                                (x1, y1 - label_size[1] - 10),
                                (x1 + label_size[0], y1),
                                color, -1)

                    # Draw label text
                    cv2.putText(annotated_image, label,
                              (x1, y1 - 5),
                              cv2.FONT_HERSHEY_SIMPLEX, 0.6,
                              (255, 255, 255), 2)

                    detection_count += 1
                    detection_info.append({
                        'class': class_name,
                        'confidence': confidence,
                        'bbox': (x1, y1, x2, y2)
                    })

        return annotated_image, detection_count, detection_info


def create_output_path(input_path, output_path=None):
    """
    Create output path with default naming convention.

    Args:
        input_path (str): Input image path
        output_path (str, optional): Specified output path

    Returns:
        str: Output path
    """
    if output_path:
        return output_path

    # Create default output path by adding "_detected" suffix
    input_path = Path(input_path)
    output_path = input_path.parent / f"{input_path.stem}_detected{input_path.suffix}"
    return str(output_path)


def validate_arguments(args):
    """
    Validate command-line arguments.

    Args:
        args: Parsed arguments from argparse

    Returns:
        bool: True if arguments are valid, False otherwise
    """
    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' does not exist.")
        return False

    # Check if input is a valid image file
    valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
    input_ext = Path(args.input).suffix.lower()
    if input_ext not in valid_extensions:
        print(f"Error: '{args.input}' does not appear to be a valid image file.")
        print(f"Supported formats: {', '.join(valid_extensions)}")
        return False

    # Check if output directory exists (if output path specified)
    if args.output:
        output_dir = Path(args.output).parent
        if not output_dir.exists():
            print(f"Error: Output directory '{output_dir}' does not exist.")
            return False

    return True


def main():
    """Main function to run the YOLO detection application."""
    parser = argparse.ArgumentParser(
        description="YOLO Object Detection for Fireworks and Insects",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --type fireworks --input image.jpg
  %(prog)s --type insects --input photo.png --output result.jpg
  %(prog)s -t fireworks -i image.jpg -c 0.5
        """
    )

    parser.add_argument(
        '--type', '-t',
        choices=['fireworks', 'insects'],
        required=True,
        help='Type of object detection to perform'
    )

    parser.add_argument(
        '--input', '-i',
        required=True,
        help='Path to the input image file'
    )

    parser.add_argument(
        '--output', '-o',
        help='Path to save the output image (default: input_detected.ext)'
    )

    parser.add_argument(
        '--confidence', '-c',
        type=float,
        default=0.25,
        help='Confidence threshold for detections (default: 0.25)'
    )

    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='Enable verbose output'
    )

    args = parser.parse_args()

    # Validate arguments
    if not validate_arguments(args):
        sys.exit(1)

    # Initialize detector
    detector = YOLODetector()

    # Load model
    if not detector.load_model(args.type):
        sys.exit(1)

    # Perform detection
    results, image = detector.detect_objects(args.input, args.confidence)
    if results is None or image is None:
        sys.exit(1)

    # Draw detections
    annotated_image, detection_count, detection_info = detector.draw_detections(image, results)

    # Create output path
    output_path = create_output_path(args.input, args.output)

    # Save annotated image
    try:
        success = cv2.imwrite(output_path, annotated_image)
        if not success:
            print(f"Error: Failed to save output image to '{output_path}'")
            sys.exit(1)
    except Exception as e:
        print(f"Error saving output image: {str(e)}")
        sys.exit(1)

    # Print results
    print(f"\nDetection Results:")
    print(f"Detection type: {args.type}")
    print(f"Objects detected: {detection_count}")
    print(f"Output saved to: {output_path}")

    if args.verbose and detection_info:
        print(f"\nDetailed Results:")
        for i, detection in enumerate(detection_info, 1):
            print(f"  {i}. {detection['class']} (confidence: {detection['confidence']:.3f})")
            x1, y1, x2, y2 = detection['bbox']
            print(f"     Bounding box: ({x1}, {y1}) to ({x2}, {y2})")

    print(f"\nDetection completed successfully!")


if __name__ == "__main__":
    main()