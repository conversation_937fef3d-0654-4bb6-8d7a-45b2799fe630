# YOLO Object Detection Command-Line Application

A command-line application for object detection using YOLO models. This application supports detection of fireworks and insects in images using pre-trained models.

## Features

- **Dual Detection Types**: Supports both fireworks and insects detection
- **Command-Line Interface**: Easy-to-use CLI with comprehensive options
- **Flexible Output**: Automatic output naming or custom output paths
- **Confidence Thresholding**: Adjustable confidence levels for detections
- **Verbose Mode**: Detailed detection information and bounding box coordinates
- **Error Handling**: Comprehensive error checking and user-friendly messages
- **Image Format Support**: Supports common image formats (JPG, PNG, BMP, TIFF, WebP)

## Requirements

- Python 3.8+
- Virtual environment (conda/venv)
- Required Python packages (automatically installed):
  - ultralytics
  - opencv-python
  - pillow

## Installation

1. **Clone or navigate to the project directory**:
   ```bash
   cd /path/to/yolo/project
   ```

2. **Activate the virtual environment**:
   ```bash
   # For conda environment
   conda activate ./venv
   
   # For standard venv
   source venv/bin/activate  # Linux/Mac
   # or
   venv\Scripts\activate     # Windows
   ```

3. **Install dependencies** (if not already installed):
   ```bash
   pip install ultralytics opencv-python pillow numpy
   ```

## Model Files

The application requires two model files in the project directory:
- `fire_smoke.pt` - For fireworks/fire/smoke detection
- `bugs.pt` - For insects detection

## Usage

### Basic Usage

```bash
python main.py --type <detection_type> --input <image_path>
```

### Command-Line Options

- `--type, -t`: Detection type (`fireworks` or `insects`) **[Required]**
- `--input, -i`: Path to input image file **[Required]**
- `--output, -o`: Path to save output image (optional)
- `--confidence, -c`: Confidence threshold (default: 0.25)
- `--verbose, -v`: Enable verbose output
- `--help, -h`: Show help message

### Examples

1. **Basic fireworks detection**:
   ```bash
   python main.py --type fireworks --input image.jpg
   ```

2. **Insects detection with custom output**:
   ```bash
   python main.py --type insects --input photo.png --output result.jpg
   ```

3. **High confidence detection with verbose output**:
   ```bash
   python main.py -t fireworks -i image.jpg -c 0.7 -v
   ```

4. **Using short options**:
   ```bash
   python main.py -t insects -i bug_photo.png -o detected_bugs.jpg -c 0.3 -v
   ```

## Output

### Default Output Naming
If no output path is specified, the application automatically creates an output filename by adding `_detected` to the input filename:
- Input: `image.jpg` → Output: `image_detected.jpg`
- Input: `photo.png` → Output: `photo_detected.png`

### Console Output
The application provides informative console output including:
- Model loading status
- Processing progress
- Detection results summary
- Output file location
- Detailed detection information (in verbose mode)

### Example Output
```
Loading fireworks detection model...
Model loaded successfully: fire_smoke.pt
Processing image: image.jpg

Detection Results:
Detection type: fireworks
Objects detected: 2
Output saved to: image_detected.jpg

Detailed Results:
  1. fire (confidence: 0.847)
     Bounding box: (120, 50) to (300, 200)
  2. smoke (confidence: 0.623)
     Bounding box: (250, 100) to (400, 250)

Detection completed successfully!
```

## Error Handling

The application includes comprehensive error handling for:

- **Invalid detection types**: Only `fireworks` and `insects` are supported
- **Missing files**: Input image and model files are validated
- **Invalid image formats**: Supports JPG, PNG, BMP, TIFF, WebP
- **Model loading issues**: Helpful messages for common problems
- **Output directory issues**: Validates output path accessibility

## Troubleshooting

### Model Loading Errors

1. **"Can't get attribute" error**: 
   - The model uses custom modules not available in the current environment
   - Ensure you have the correct ultralytics version
   - Contact the model provider for custom modules

2. **"No module named" error**:
   - Missing dependencies
   - Run: `pip install ultralytics opencv-python pillow`

3. **CUDA errors**:
   - GPU-related issues
   - The application will attempt to run on CPU

### Common Issues

1. **File not found**: Check that image and model files exist
2. **Permission errors**: Ensure write permissions for output directory
3. **Memory errors**: Try reducing image size or using CPU mode

## Supported Image Formats

- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- TIFF (.tiff, .tif)
- WebP (.webp)

## Performance Notes

- First run may be slower due to model initialization
- GPU acceleration is used when available
- Processing time depends on image size and model complexity
- Typical processing time: 10-100ms per image

## License

This application is provided as-is for educational and research purposes.

## Support

For issues or questions:
1. Check the error messages for specific guidance
2. Verify all requirements are installed
3. Ensure model files are present and accessible
4. Check image file format and accessibility
