#ifndef Py_INTERNAL_UNICODEOBJECT_GENERATED_H
#define Py_INTERNAL_UNICODEOBJECT_GENERATED_H
#ifdef __cplusplus
extern "C" {
#endif

#ifndef Py_BUILD_CORE
#  error "this header requires Py_BUILD_CORE define"
#endif

/* The following is auto-generated by Tools/build/generate_global_objects.py. */
static inline void
_PyUnicode_InitStaticStrings(PyInterpreterState *interp) {
    PyObject *string;
    string = &_Py_ID(CANCELLED);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(FINISHED);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(False);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(JSONDecodeError);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(PENDING);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(Py_Repr);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(TextIOWrapper);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(True);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(WarningMessage);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_WindowsConsoleIO);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__IOBase_closed);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__abc_tpflags__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__abs__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__abstractmethods__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__add__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__aenter__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__aexit__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__aiter__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__all__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__and__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__anext__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__annotations__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__args__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__asyncio_running_event_loop__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__await__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__bases__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__bool__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__buffer__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__build_class__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__builtins__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__bytes__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__call__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__cantrace__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__class__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__class_getitem__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__classcell__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__classdict__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__classdictcell__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__complex__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__contains__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__copy__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__ctypes_from_outparam__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__del__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__delattr__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__delete__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__delitem__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__dict__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__dictoffset__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__dir__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__divmod__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__doc__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__enter__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__eq__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__exit__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__file__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__float__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__floordiv__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__format__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__fspath__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__ge__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__get__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__getattr__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__getattribute__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__getinitargs__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__getitem__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__getnewargs__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__getnewargs_ex__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__getstate__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__gt__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__hash__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__iadd__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__iand__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__ifloordiv__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__ilshift__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__imatmul__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__imod__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__import__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__imul__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__index__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__init__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__init_subclass__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__instancecheck__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__int__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__invert__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__ior__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__ipow__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__irshift__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__isabstractmethod__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__isub__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__iter__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__itruediv__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__ixor__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__le__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__len__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__length_hint__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__lltrace__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__loader__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__lshift__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__lt__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__main__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__matmul__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__missing__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__mod__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__module__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__mro_entries__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__mul__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__name__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__ne__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__neg__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__new__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__newobj__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__newobj_ex__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__next__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__notes__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__or__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__orig_class__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__origin__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__package__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__parameters__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__path__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__pos__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__pow__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__prepare__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__qualname__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__radd__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__rand__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__rdivmod__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__reduce__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__reduce_ex__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__release_buffer__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__repr__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__reversed__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__rfloordiv__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__rlshift__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__rmatmul__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__rmod__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__rmul__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__ror__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__round__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__rpow__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__rrshift__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__rshift__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__rsub__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__rtruediv__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__rxor__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__set__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__set_name__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__setattr__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__setitem__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__setstate__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__sizeof__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__slotnames__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__slots__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__spec__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__str__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__sub__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__subclasscheck__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__subclasshook__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__truediv__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__trunc__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__type_params__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__typing_is_unpacked_typevartuple__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__typing_prepare_subst__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__typing_subst__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__typing_unpacked_tuple_args__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__warningregistry__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__weaklistoffset__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__weakref__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(__xor__);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_abc_impl);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_abstract_);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_active);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_annotation);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_anonymous_);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_argtypes_);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_as_parameter_);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_asyncio_future_blocking);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_blksize);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_bootstrap);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_check_retval_);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_dealloc_warn);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_feature_version);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_fields_);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_finalizing);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_find_and_load);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_fix_up_module);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_flags_);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_get_sourcefile);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_handle_fromlist);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_initializing);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_io);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_is_text_encoding);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_length_);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_limbo);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_lock_unlock_module);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_loop);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_needs_com_addref_);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_only_immortal);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_pack_);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_restype_);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_showwarnmsg);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_shutdown);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_slotnames);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_strptime_datetime);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_swappedbytes_);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_type_);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_uninitialized_submodules);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_warn_unawaited_coroutine);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(_xoptions);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(abs_tol);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(access);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(add);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(add_done_callback);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(after_in_child);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(after_in_parent);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(aggregate_class);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(alias);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(append);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(arg);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(argdefs);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(args);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(arguments);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(argv);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(as_integer_ratio);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(ast);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(attribute);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(authorizer_callback);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(autocommit);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(backtick);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(base);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(before);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(big);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(binary_form);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(block);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(bound);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(buffer);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(buffer_callback);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(buffer_size);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(buffering);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(buffers);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(bufsize);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(builtins);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(byteorder);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(bytes);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(bytes_per_sep);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(c_call);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(c_exception);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(c_return);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(cached_statements);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(cadata);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(cafile);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(call);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(call_exception_handler);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(call_soon);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(cancel);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(capath);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(category);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(cb_type);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(certfile);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(check_same_thread);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(clear);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(close);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(closed);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(closefd);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(closure);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_argcount);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_cellvars);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_code);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_consts);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_exceptiontable);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_filename);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_firstlineno);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_flags);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_freevars);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_kwonlyargcount);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_linetable);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_name);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_names);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_nlocals);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_posonlyargcount);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_qualname);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_stacksize);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(co_varnames);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(code);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(command);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(comment_factory);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(compile_mode);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(consts);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(context);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(contravariant);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(cookie);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(copy);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(copyreg);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(coro);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(count);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(covariant);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(cwd);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(data);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(database);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(decode);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(decoder);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(default);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(defaultaction);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(delete);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(depth);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(detect_types);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(deterministic);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(device);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(dict);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(dictcomp);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(difference_update);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(digest);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(digest_size);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(digestmod);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(dir_fd);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(discard);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(dispatch_table);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(displayhook);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(dklen);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(doc);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(dont_inherit);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(dst);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(dst_dir_fd);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(duration);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(eager_start);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(effective_ids);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(element_factory);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(encode);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(encoding);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(end);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(end_lineno);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(end_offset);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(endpos);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(entrypoint);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(env);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(errors);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(event);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(eventmask);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(exc_type);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(exc_value);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(excepthook);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(exception);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(existing_file_name);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(exp);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(extend);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(extra_tokens);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(facility);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(factory);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(false);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(family);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(fanout);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(fd);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(fd2);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(fdel);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(fget);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(file);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(file_actions);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(filename);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(fileno);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(filepath);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(fillvalue);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(filters);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(final);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(find_class);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(fix_imports);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(flags);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(flush);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(follow_symlinks);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(format);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(frequency);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(from_param);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(fromlist);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(fromtimestamp);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(fromutc);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(fset);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(func);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(future);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(generation);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(genexpr);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(get);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(get_debug);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(get_event_loop);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(get_loop);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(get_source);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(getattr);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(getstate);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(gid);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(globals);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(groupindex);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(groups);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(handle);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(hash_name);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(header);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(headers);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(hi);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(hook);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(id);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(ident);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(ignore);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(imag);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(importlib);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(in_fd);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(incoming);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(indexgroup);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(inf);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(infer_variance);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(inheritable);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(initial);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(initial_bytes);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(initial_value);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(initval);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(inner_size);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(input);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(insert_comments);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(insert_pis);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(instructions);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(intern);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(intersection);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(is_running);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(isatty);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(isinstance);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(isoformat);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(isolation_level);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(istext);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(item);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(items);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(iter);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(iterable);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(iterations);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(join);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(jump);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(keepends);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(key);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(keyfile);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(keys);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(kind);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(kw);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(kw1);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(kw2);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(lambda);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(last);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(last_exc);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(last_node);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(last_traceback);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(last_type);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(last_value);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(latin1);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(leaf_size);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(len);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(length);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(level);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(limit);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(line);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(line_buffering);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(lineno);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(listcomp);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(little);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(lo);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(locale);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(locals);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(logoption);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(loop);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(mapping);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(match);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(max_length);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(maxdigits);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(maxevents);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(maxmem);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(maxsplit);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(maxvalue);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(memLevel);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(memlimit);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(message);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(metaclass);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(metadata);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(method);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(mod);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(mode);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(module);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(module_globals);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(modules);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(mro);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(msg);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(mycmp);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(n_arg);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(n_fields);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(n_sequence_fields);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(n_unnamed_fields);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(name);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(name_from);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(namespace_separator);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(namespaces);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(narg);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(ndigits);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(new_file_name);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(new_limit);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(newline);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(newlines);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(next);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(nlocals);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(node_depth);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(node_offset);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(ns);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(nstype);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(nt);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(null);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(number);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(obj);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(object);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(offset);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(offset_dst);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(offset_src);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(on_type_read);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(onceregistry);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(only_keys);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(oparg);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(opcode);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(open);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(opener);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(operation);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(optimize);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(options);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(order);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(origin);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(out_fd);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(outgoing);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(overlapped);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(owner);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(pages);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(parent);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(password);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(path);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(pattern);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(peek);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(persistent_id);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(persistent_load);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(person);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(pi_factory);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(pid);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(policy);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(pos);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(pos1);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(pos2);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(posix);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(print_file_and_line);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(priority);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(progress);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(progress_handler);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(progress_routine);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(proto);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(protocol);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(ps1);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(ps2);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(query);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(quotetabs);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(raw);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(read);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(read1);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(readable);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(readall);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(readinto);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(readinto1);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(readline);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(readonly);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(real);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(reducer_override);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(registry);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(rel_tol);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(release);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(reload);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(repl);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(replace);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(reserved);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(reset);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(resetids);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(return);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(reverse);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(reversed);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(salt);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(sched_priority);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(scheduler);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(seek);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(seekable);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(selectors);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(self);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(send);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(sep);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(sequence);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(server_hostname);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(server_side);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(session);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(setcomp);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(setpgroup);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(setsid);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(setsigdef);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(setsigmask);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(setstate);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(shape);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(show_cmd);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(signed);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(size);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(sizehint);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(skip_file_prefixes);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(sleep);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(sock);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(sort);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(sound);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(source);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(source_traceback);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(spam);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(src);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(src_dir_fd);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(stacklevel);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(start);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(statement);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(status);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(stderr);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(stdin);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(stdout);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(step);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(steps);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(store_name);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(strategy);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(strftime);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(strict);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(strict_mode);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(string);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(sub_key);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(symmetric_difference_update);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(tabsize);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(tag);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(target);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(target_is_directory);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(task);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(tb_frame);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(tb_lasti);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(tb_lineno);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(tb_next);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(tell);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(template);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(term);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(text);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(threading);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(throw);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(timeout);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(times);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(timetuple);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(top);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(trace_callback);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(traceback);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(trailers);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(translate);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(true);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(truncate);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(twice);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(txt);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(type);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(type_params);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(tz);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(tzname);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(uid);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(unlink);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(unraisablehook);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(uri);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(usedforsecurity);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(value);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(values);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(version);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(volume);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(warnings);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(warnoptions);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(wbits);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(week);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(weekday);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(which);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(who);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(withdata);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(writable);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(write);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(write_through);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(year);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_ID(zdict);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(empty);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(dbl_percent);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(dot_locals);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(defaults);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(generic_base);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(kwdefaults);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(type_params);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(anon_dictcomp);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(anon_genexpr);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(anon_lambda);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(anon_listcomp);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(anon_module);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(anon_setcomp);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(shim_name);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(anon_string);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(anon_unknown);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(json_decoder);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(list_err);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(utf_8);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(dbl_open_br);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
    string = &_Py_STR(dbl_close_br);
    _PyUnicode_InternStatic(interp, &string);
    assert(_PyUnicode_CheckConsistency(string, 1));
    assert(PyUnicode_GET_LENGTH(string) != 1);
}
/* End auto-generated code */
#ifdef __cplusplus
}
#endif
#endif /* !Py_INTERNAL_UNICODEOBJECT_GENERATED_H */
