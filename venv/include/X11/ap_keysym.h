/******************************************************************
Copyright 1987 by Apollo Computer Inc., Chelmsford, Massachusetts.
Copyright 1989 by Hewlett-Packard Company.

                        All Rights Reserved

Permission to use, duplicate, change, and distribute this software and
its documentation for any purpose and without fee is granted, provided
that the above copyright notice appear in such copy and that this
copyright notice appear in all supporting documentation, and that the
names of Apollo Computer Inc., the Hewlett-Packard Company, or the X
Consortium not be used in advertising or publicity pertaining to
distribution of the software without written prior permission.

HEWLETT-PACKARD MAKES NO WARRANTY OF ANY KIND WITH REGARD
TO THIS SOFTWARE, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
PURPOSE.  <PERSON><PERSON><PERSON><PERSON><PERSON> shall not be liable for errors
contained herein or direct, indirect, special, incidental or
consequential damages in connection with the furnishing,
performance, or use of this material.

This software is not subject to any license of the American
Telephone and Telegraph Company or of the Regents of the
University of California.
******************************************************************/

#define apXK_LineDel            0x1000FF00
#define apXK_CharDel            0x1000FF01
#define apXK_Copy               0x1000FF02
#define apXK_Cut                0x1000FF03
#define apXK_Paste              0x1000FF04
#define apXK_Move               0x1000FF05
#define apXK_Grow               0x1000FF06
#define apXK_Cmd                0x1000FF07
#define apXK_Shell              0x1000FF08
#define apXK_LeftBar            0x1000FF09
#define apXK_RightBar           0x1000FF0A
#define apXK_LeftBox            0x1000FF0B
#define apXK_RightBox           0x1000FF0C
#define apXK_UpBox              0x1000FF0D
#define apXK_DownBox            0x1000FF0E
#define apXK_Pop                0x1000FF0F
#define apXK_Read               0x1000FF10
#define apXK_Edit               0x1000FF11
#define apXK_Save               0x1000FF12
#define apXK_Exit               0x1000FF13
#define apXK_Repeat             0x1000FF14

#define apXK_KP_parenleft       0x1000FFA8
#define apXK_KP_parenright      0x1000FFA9
