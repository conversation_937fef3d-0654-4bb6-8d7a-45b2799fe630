%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(TM) 5.5
%%For: (Bud Northern) (<PERSON>)
%%Title: (TCL PWRD LOGO.ILLUS)
%%CreationDate: (8/1/96) (4:59 PM)
%%BoundingBox: 242 302 377 513
%%HiResBoundingBox: 242.0523 302.5199 376.3322 512.5323
%%DocumentProcessColors: Cyan <PERSON>
%%DocumentSuppliedResources: procset Adobe_level2_AI5 1.0 0
%%+ procset Adobe_IllustratorA_AI5 1.0 0
%AI5_FileFormat 1.2
%AI3_ColorUsage: Color
%%CMYKCustomColor: 0 0.45 1 0 (Orange)
%%+ 0 0.25 1 0 (Orange Yellow)
%%+ 0 0.79 0.91 0 (PANTONE Warm Red CV)
%%+ 0 0.79 0.91 0 (TCL RED)
%AI3_TemplateBox: 306 396 306 396
%AI3_TileBox: 12 12 600 780
%AI3_DocumentPreview: Macintosh_ColorPic
%AI5_ArtSize: 612 792
%AI5_RulerUnits: 0
%AI5_ArtFlags: 1 0 0 1 0 0 1 1 0
%AI5_TargetResolution: 800
%AI5_NumLayers: 1
%AI5_OpenToView: 102 564 2 938 673 18 1 1 2 40
%AI5_OpenViewLayers: 7
%%EndComments
%%BeginProlog
%%BeginResource: procset Adobe_level2_AI5 1.0 0
%%Title: (Adobe Illustrator (R) Version 5.0 Level 2 Emulation)
%%Version: 1.0 
%%CreationDate: (04/10/93) ()
%%Copyright: ((C) 1987-1993 Adobe Systems Incorporated All Rights Reserved)
userdict /Adobe_level2_AI5 21 dict dup begin
	put
	/packedarray where not
	{
		userdict begin
		/packedarray
		{
			array astore readonly
		} bind def
		/setpacking /pop load def
		/currentpacking false def
	 end
		0
	} if
	pop
	userdict /defaultpacking currentpacking put true setpacking
	/initialize
	{
		Adobe_level2_AI5 begin
	} bind def
	/terminate
	{
		currentdict Adobe_level2_AI5 eq
		{
		 end
		} if
	} bind def
	mark
	/setcustomcolor where not
	{
		/findcmykcustomcolor
		{
			5 packedarray
		} bind def
		/setcustomcolor
		{
			exch aload pop pop
			4
			{
				4 index mul 4 1 roll
			} repeat
			5 -1 roll pop
			setcmykcolor
		}
		def
	} if
	
	/gt38? mark {version cvx exec} stopped {cleartomark true} {38 gt exch pop} ifelse def
	userdict /deviceDPI 72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt put
	userdict /level2?
	systemdict /languagelevel known dup
	{
		pop systemdict /languagelevel get 2 ge
	} if
	put
	level2? not
	{
		/setcmykcolor where not
		{
			/setcmykcolor
			{
				exch .11 mul add exch .59 mul add exch .3 mul add
				1 exch sub setgray
			} def
		} if
		/currentcmykcolor where not
		{
			/currentcmykcolor
			{
				0 0 0 1 currentgray sub
			} def
		} if
		/setoverprint where not
		{
			/setoverprint /pop load def
		} if
		/selectfont where not
		{
			/selectfont
			{
				exch findfont exch
				dup type /arraytype eq
				{
					makefont
				}
				{
					scalefont
				} ifelse
				setfont
			} bind def
		} if
		/cshow where not
		{
			/cshow
			{
				[
				0 0 5 -1 roll aload pop
				] cvx bind forall
			} bind def
		} if
	} if
	cleartomark
	/anyColor?
	{
		add add add 0 ne
	} bind def
	/testColor
	{
		gsave
		setcmykcolor currentcmykcolor
		grestore
	} bind def
	/testCMYKColorThrough
	{
		testColor anyColor?
	} bind def
	userdict /composite?
	level2?
	{
		gsave 1 1 1 1 setcmykcolor currentcmykcolor grestore
		add add add 4 eq
	}
	{
		1 0 0 0 testCMYKColorThrough
		0 1 0 0 testCMYKColorThrough
		0 0 1 0 testCMYKColorThrough
		0 0 0 1 testCMYKColorThrough
		and and and
	} ifelse
	put
	composite? not
	{
		userdict begin
		gsave
		/cyan? 1 0 0 0 testCMYKColorThrough def
		/magenta? 0 1 0 0 testCMYKColorThrough def
		/yellow? 0 0 1 0 testCMYKColorThrough def
		/black? 0 0 0 1 testCMYKColorThrough def
		grestore
		/isCMYKSep? cyan? magenta? yellow? black? or or or def
		/customColor? isCMYKSep? not def
	 end
	} if
 end defaultpacking setpacking
%%EndResource
%%BeginResource: procset Adobe_IllustratorA_AI5 1.1 0
%%Title: (Adobe Illustrator (R) Version 5.0 Abbreviated Prolog)
%%Version: 1.1 
%%CreationDate: (3/7/1994) ()
%%Copyright: ((C) 1987-1994 Adobe Systems Incorporated All Rights Reserved)
currentpacking true setpacking
userdict /Adobe_IllustratorA_AI5_vars 70 dict dup begin
put
/_lp /none def
/_pf
{
} def
/_ps
{
} def
/_psf
{
} def
/_pss
{
} def
/_pjsf
{
} def
/_pjss
{
} def
/_pola 0 def
/_doClip 0 def
/cf currentflat def
/_tm matrix def
/_renderStart
[
/e0 /r0 /a0 /o0 /e1 /r1 /a1 /i0
] def
/_renderEnd
[
null null null null /i1 /i1 /i1 /i1
] def
/_render -1 def
/_rise 0 def
/_ax 0 def
/_ay 0 def
/_cx 0 def
/_cy 0 def
/_leading
[
0 0
] def
/_ctm matrix def
/_mtx matrix def
/_sp 16#020 def
/_hyphen (-) def
/_fScl 0 def
/_cnt 0 def
/_hs 1 def
/_nativeEncoding 0 def
/_useNativeEncoding 0 def
/_tempEncode 0 def
/_pntr 0 def
/_tDict 2 dict def
/_wv 0 def
/Tx
{
} def
/Tj
{
} def
/CRender
{
} def
/_AI3_savepage
{
} def
/_gf null def
/_cf 4 array def
/_if null def
/_of false def
/_fc
{
} def
/_gs null def
/_cs 4 array def
/_is null def
/_os false def
/_sc
{
} def
/discardSave null def
/buffer 256 string def
/beginString null def
/endString null def
/endStringLength null def
/layerCnt 1 def
/layerCount 1 def
/perCent (%) 0 get def
/perCentSeen? false def
/newBuff null def
/newBuffButFirst null def
/newBuffLast null def
/clipForward? false def
end
userdict /Adobe_IllustratorA_AI5 74 dict dup begin
put
/initialize
{
	Adobe_IllustratorA_AI5 dup begin
	Adobe_IllustratorA_AI5_vars begin
	discardDict
	{
		bind pop pop
	} forall
	dup /nc get begin
	{
		dup xcheck 1 index type /operatortype ne and
		{
			bind
		} if
		pop pop
	} forall
 end
	newpath
} def
/terminate
{
 end
 end
} def
/_
null def
/ddef
{
	Adobe_IllustratorA_AI5_vars 3 1 roll put
} def
/xput
{
	dup load dup length exch maxlength eq
	{
		dup dup load dup
		length 2 mul dict copy def
	} if
	load begin
	def
 end
} def
/npop
{
	{
		pop
	} repeat
} def
/sw
{
	dup length exch stringwidth
	exch 5 -1 roll 3 index mul add
	4 1 roll 3 1 roll mul add
} def
/swj
{
	dup 4 1 roll
	dup length exch stringwidth
	exch 5 -1 roll 3 index mul add
	4 1 roll 3 1 roll mul add
	6 2 roll /_cnt 0 ddef
	{
		1 index eq
		{
			/_cnt _cnt 1 add ddef
		} if
	} forall
	pop
	exch _cnt mul exch _cnt mul 2 index add 4 1 roll 2 index add 4 1 roll pop pop
} def
/ss
{
	4 1 roll
	{
		2 npop
		(0) exch 2 copy 0 exch put pop
		gsave
		false charpath currentpoint
		4 index setmatrix
		stroke
		grestore
		moveto
		2 copy rmoveto
	} exch cshow
	3 npop
} def
/jss
{
	4 1 roll
	{
		2 npop
		(0) exch 2 copy 0 exch put
		gsave
		_sp eq
		{
			exch 6 index 6 index 6 index 5 -1 roll widthshow
			currentpoint
		}
		{
			false charpath currentpoint
			4 index setmatrix stroke
		} ifelse
		grestore
		moveto
		2 copy rmoveto
	} exch cshow
	6 npop
} def
/sp
{
	{
		2 npop (0) exch
		2 copy 0 exch put pop
		false charpath
		2 copy rmoveto
	} exch cshow
	2 npop
} def
/jsp
{
	{
		2 npop
		(0) exch 2 copy 0 exch put
		_sp eq
		{
			exch 5 index 5 index 5 index 5 -1 roll widthshow
		}
		{
			false charpath
		} ifelse
		2 copy rmoveto
	} exch cshow
	5 npop
} def
/pl
{
	transform
	0.25 sub round 0.25 add exch
	0.25 sub round 0.25 add exch
	itransform
} def
/setstrokeadjust where
{
	pop true setstrokeadjust
	/c
	{
		curveto
	} def
	/C
	/c load def
	/v
	{
		currentpoint 6 2 roll curveto
	} def
	/V
	/v load def
	/y
	{
		2 copy curveto
	} def
	/Y
	/y load def
	/l
	{
		lineto
	} def
	/L
	/l load def
	/m
	{
		moveto
	} def
}
{
	/c
	{
		pl curveto
	} def
	/C
	/c load def
	/v
	{
		currentpoint 6 2 roll pl curveto
	} def
	/V
	/v load def
	/y
	{
		pl 2 copy curveto
	} def
	/Y
	/y load def
	/l
	{
		pl lineto
	} def
	/L
	/l load def
	/m
	{
		pl moveto
	} def
} ifelse
/d
{
	setdash
} def
/cf
{
} def
/i
{
	dup 0 eq
	{
		pop cf
	} if
	setflat
} def
/j
{
	setlinejoin
} def
/J
{
	setlinecap
} def
/M
{
	setmiterlimit
} def
/w
{
	setlinewidth
} def
/H
{
} def
/h
{
	closepath
} def
/N
{
	_pola 0 eq
	{
		_doClip 1 eq
		{
			clip /_doClip 0 ddef
		} if
		newpath
	}
	{
		/CRender
		{
			N
		} ddef
	} ifelse
} def
/n
{
	N
} def
/F
{
	_pola 0 eq
	{
		_doClip 1 eq
		{
			gsave _pf grestore clip newpath /_lp /none ddef _fc
			/_doClip 0 ddef
		}
		{
			_pf
		} ifelse
	}
	{
		/CRender
		{
			F
		} ddef
	} ifelse
} def
/f
{
	closepath
	F
} def
/S
{
	_pola 0 eq
	{
		_doClip 1 eq
		{
			gsave _ps grestore clip newpath /_lp /none ddef _sc
			/_doClip 0 ddef
		}
		{
			_ps
		} ifelse
	}
	{
		/CRender
		{
			S
		} ddef
	} ifelse
} def
/s
{
	closepath
	S
} def
/B
{
	_pola 0 eq
	{
		_doClip 1 eq
		gsave F grestore
		{
			gsave S grestore clip newpath /_lp /none ddef _sc
			/_doClip 0 ddef
		}
		{
			S
		} ifelse
	}
	{
		/CRender
		{
			B
		} ddef
	} ifelse
} def
/b
{
	closepath
	B
} def
/W
{
	/_doClip 1 ddef
} def
/*
{
	count 0 ne
	{
		dup type /stringtype eq
		{
			pop
		} if
	} if
	newpath
} def
/u
{
} def
/U
{
} def
/q
{
	_pola 0 eq
	{
		gsave
	} if
} def
/Q
{
	_pola 0 eq
	{
		grestore
	} if
} def
/*u
{
	_pola 1 add /_pola exch ddef
} def
/*U
{
	_pola 1 sub /_pola exch ddef
	_pola 0 eq
	{
		CRender
	} if
} def
/D
{
	pop
} def
/*w
{
} def
/*W
{
} def
/`
{
	/_i save ddef
	clipForward?
	{
		nulldevice
	} if
	6 1 roll 4 npop
	concat pop
	userdict begin
	/showpage
	{
	} def
	0 setgray
	0 setlinecap
	1 setlinewidth
	0 setlinejoin
	10 setmiterlimit
	[] 0 setdash
	/setstrokeadjust where {pop false setstrokeadjust} if
	newpath
	0 setgray
	false setoverprint
} def
/~
{
 end
	_i restore
} def
/O
{
	0 ne
	/_of exch ddef
	/_lp /none ddef
} def
/R
{
	0 ne
	/_os exch ddef
	/_lp /none ddef
} def
/g
{
	/_gf exch ddef
	/_fc
	{
		_lp /fill ne
		{
			_of setoverprint
			_gf setgray
			/_lp /fill ddef
		} if
	} ddef
	/_pf
	{
		_fc
		fill
	} ddef
	/_psf
	{
		_fc
		ashow
	} ddef
	/_pjsf
	{
		_fc
		awidthshow
	} ddef
	/_lp /none ddef
} def
/G
{
	/_gs exch ddef
	/_sc
	{
		_lp /stroke ne
		{
			_os setoverprint
			_gs setgray
			/_lp /stroke ddef
		} if
	} ddef
	/_ps
	{
		_sc
		stroke
	} ddef
	/_pss
	{
		_sc
		ss
	} ddef
	/_pjss
	{
		_sc
		jss
	} ddef
	/_lp /none ddef
} def
/k
{
	_cf astore pop
	/_fc
	{
		_lp /fill ne
		{
			_of setoverprint
			_cf aload pop setcmykcolor
			/_lp /fill ddef
		} if
	} ddef
	/_pf
	{
		_fc
		fill
	} ddef
	/_psf
	{
		_fc
		ashow
	} ddef
	/_pjsf
	{
		_fc
		awidthshow
	} ddef
	/_lp /none ddef
} def
/K
{
	_cs astore pop
	/_sc
	{
		_lp /stroke ne
		{
			_os setoverprint
			_cs aload pop setcmykcolor
			/_lp /stroke ddef
		} if
	} ddef
	/_ps
	{
		_sc
		stroke
	} ddef
	/_pss
	{
		_sc
		ss
	} ddef
	/_pjss
	{
		_sc
		jss
	} ddef
	/_lp /none ddef
} def
/x
{
	/_gf exch ddef
	findcmykcustomcolor
	/_if exch ddef
	/_fc
	{
		_lp /fill ne
		{
			_of setoverprint
			_if _gf 1 exch sub setcustomcolor
			/_lp /fill ddef
		} if
	} ddef
	/_pf
	{
		_fc
		fill
	} ddef
	/_psf
	{
		_fc
		ashow
	} ddef
	/_pjsf
	{
		_fc
		awidthshow
	} ddef
	/_lp /none ddef
} def
/X
{
	/_gs exch ddef
	findcmykcustomcolor
	/_is exch ddef
	/_sc
	{
		_lp /stroke ne
		{
			_os setoverprint
			_is _gs 1 exch sub setcustomcolor
			/_lp /stroke ddef
		} if
	} ddef
	/_ps
	{
		_sc
		stroke
	} ddef
	/_pss
	{
		_sc
		ss
	} ddef
	/_pjss
	{
		_sc
		jss
	} ddef
	/_lp /none ddef
} def
/A
{
	pop
} def
/annotatepage
{
userdict /annotatepage 2 copy known {get exec} {pop pop} ifelse
} def
/discard
{
	save /discardSave exch store
	discardDict begin
	/endString exch store
	gt38?
	{
		2 add
	} if
	load
	stopped
	pop
 end
	discardSave restore
} bind def
userdict /discardDict 7 dict dup begin
put
/pre38Initialize
{
	/endStringLength endString length store
	/newBuff buffer 0 endStringLength getinterval store
	/newBuffButFirst newBuff 1 endStringLength 1 sub getinterval store
	/newBuffLast newBuff endStringLength 1 sub 1 getinterval store
} def
/shiftBuffer
{
	newBuff 0 newBuffButFirst putinterval
	newBuffLast 0
	currentfile read not
	{
	stop
	} if
	put
} def
0
{
	pre38Initialize
	mark
	currentfile newBuff readstring exch pop
	{
		{
			newBuff endString eq
			{
				cleartomark stop
			} if
			shiftBuffer
		} loop
	}
	{
	stop
	} ifelse
} def
1
{
	pre38Initialize
	/beginString exch store
	mark
	currentfile newBuff readstring exch pop
	{
		{
			newBuff beginString eq
			{
				/layerCount dup load 1 add store
			}
			{
				newBuff endString eq
				{
					/layerCount dup load 1 sub store
					layerCount 0 eq
					{
						cleartomark stop
					} if
				} if
			} ifelse
			shiftBuffer
		} loop
	}
	{
	stop
	} ifelse
} def
2
{
	mark
	{
		currentfile buffer readline not
		{
		stop
		} if
		endString eq
		{
			cleartomark stop
		} if
	} loop
} def
3
{
	/beginString exch store
	/layerCnt 1 store
	mark
	{
		currentfile buffer readline not
		{
		stop
		} if
		dup beginString eq
		{
			pop /layerCnt dup load 1 add store
		}
		{
			endString eq
			{
				layerCnt 1 eq
				{
					cleartomark stop
				}
				{
					/layerCnt dup load 1 sub store
				} ifelse
			} if
		} ifelse
	} loop
} def
end
userdict /clipRenderOff 15 dict dup begin
put
{
	/n /N /s /S /f /F /b /B
}
{
	{
		_doClip 1 eq
		{
			/_doClip 0 ddef clip
		} if
		newpath
	} def
} forall
/Tr /pop load def
/Bb {} def
/BB /pop load def
/Bg {12 npop} def
/Bm {6 npop} def
/Bc /Bm load def
/Bh {4 npop} def
end
/Lb
{
	4 npop
	6 1 roll
	pop
	4 1 roll
	pop pop pop
	0 eq
	{
		0 eq
		{
			(%AI5_BeginLayer) 1 (%AI5_EndLayer--) discard
		}
		{
			/clipForward? true def
			
			/Tx /pop load def
			/Tj /pop load def
			currentdict end clipRenderOff begin begin
		} ifelse
	}
	{
		0 eq
		{
			save /discardSave exch store
		} if
	} ifelse
} bind def
/LB
{
	discardSave dup null ne
	{
		restore
	}
	{
		pop
		clipForward?
		{
			currentdict
		 end
		 end
		 begin
			
			/clipForward? false ddef
		} if
	} ifelse
} bind def
/Pb
{
	pop pop
	0 (%AI5_EndPalette) discard
} bind def
/Np
{
	0 (%AI5_End_NonPrinting--) discard
} bind def
/Ln /pop load def
/Ap
/pop load def
/Ar
{
	72 exch div
	0 dtransform dup mul exch dup mul add sqrt
	dup 1 lt
	{
		pop 1
	} if
	setflat
} def
/Mb
{
	q
} def
/Md
{
} def
/MB
{
	Q
} def
/nc 3 dict def
nc begin
/setgray
{
	pop
} bind def
/setcmykcolor
{
	4 npop
} bind def
/setcustomcolor
{
	2 npop
} bind def
currentdict readonly pop
end
currentdict readonly pop
end
setpacking
%%EndResource
%%EndProlog
%%BeginSetup
Adobe_level2_AI5 /initialize get exec
Adobe_IllustratorA_AI5 /initialize get exec
%AI5_Begin_NonPrinting
Np
%AI3_BeginPattern: (Yellow Stripe)
(Yellow Stripe) 8.4499 4.6 80.4499 76.6 [
%AI3_Tile
(0 O 0 R 0 0.4 1 0 k 0 0.4 1 0 K) @
(
800 Ar
0 J 0 j 3.6 w 4 M []0 d
%AI3_Note:
0 D
8.1999 8.1999 m
80.6999 8.1999 L
S
8.1999 22.6 m
80.6999 22.6 L
S
8.1999 37.0001 m
80.6999 37.0001 L
S
8.1999 51.3999 m
80.6999 51.3999 L
S
8.1999 65.8 m
80.6999 65.8 L
S
8.1999 15.3999 m
80.6999 15.3999 L
S
8.1999 29.8 m
80.6999 29.8 L
S
8.1999 44.1999 m
80.6999 44.1999 L
S
8.1999 58.6 m
80.6999 58.6 L
S
8.1999 73.0001 m
80.6999 73.0001 L
S
) &
] E
%AI3_EndPattern
%AI5_End_NonPrinting--
%AI5_Begin_NonPrinting
Np
3 Bn
%AI5_BeginGradient: (Black & White)
(Black & White) 0 2 Bd
[
<
FFFEFDFCFBFAF9F8F7F6F5F4F3F2F1F0EFEEEDECEBEAE9E8E7E6E5E4E3E2E1E0DFDEDDDCDBDAD9D8
D7D6D5D4D3D2D1D0CFCECDCCCBCAC9C8C7C6C5C4C3C2C1C0BFBEBDBCBBBAB9B8B7B6B5B4B3B2B1B0
AFAEADACABAAA9A8A7A6A5A4A3A2A1A09F9E9D9C9B9A999897969594939291908F8E8D8C8B8A8988
87868584838281807F7E7D7C7B7A797877767574737271706F6E6D6C6B6A69686766656463626160
5F5E5D5C5B5A595857565554535251504F4E4D4C4B4A494847464544434241403F3E3D3C3B3A3938
37363534333231302F2E2D2C2B2A292827262524232221201F1E1D1C1B1A19181716151413121110
0F0E0D0C0B0A09080706050403020100
>
0 %_Br
[
0 0 50 100 %_Bs
1 0 50 0 %_Bs
BD
%AI5_EndGradient
%AI5_BeginGradient: (Red & Yellow)
(Red & Yellow) 0 2 Bd
[
0
<
000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
28292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F
505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071727374757677
78797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9F
A0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
C8C9CACBCCCDCECFD0D1D2D3D4D5D6D7D8D9DADBDCDDDEDFE0E1E2E3E4E5E6E7E8E9EAEBECEDEEEF
F0F1F2F3F4F5F6F7F8F9FAFBFCFDFEFF
>
<
FFFFFEFEFDFDFDFCFCFBFBFBFAFAF9F9F9F8F8F7F7F7F6F6F5F5F5F4F4F3F3F3F2F2F1F1F1F0F0EF
EFEFEEEEEDEDEDECECEBEBEBEAEAE9E9E9E8E8E7E7E7E6E6E5E5E5E4E4E3E3E3E2E2E1E1E1E0E0DF
DFDFDEDEDDDDDDDCDCDBDBDBDADAD9D9D9D8D8D7D7D7D6D6D5D5D5D4D4D3D3D3D2D2D1D1D1D0D0CF
CFCFCECECDCDCDCCCCCBCBCBCACAC9C9C9C8C8C7C7C7C6C6C5C5C5C4C4C3C3C3C2C2C1C1C1C0C0BF
BFBFBEBEBDBDBDBCBCBBBBBBBABAB9B9B9B8B8B7B7B7B6B6B5B5B5B4B4B3B3B3B2B2B1B1B1B0B0AF
AFAFAEAEADADADACACABABABAAAAA9A9A9A8A8A7A7A7A6A6A5A5A5A4A4A3A3A3A2A2A1A1A1A0A09F
9F9F9E9E9D9D9D9C9C9B9B9B9A9A9999
>
0
1 %_Br
[
0 1 0.6 0 1 50 100 %_Bs
0 0 1 0 1 50 0 %_Bs
BD
%AI5_EndGradient
%AI5_BeginGradient: (Yellow & Blue Radial)
(Yellow & Blue Radial) 1 2 Bd
[
<
000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F2021222324252627
28292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F
505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F7071727374757677
78797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9F
A0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
C8C9CACBCCCDCECFD0D1D2D3D4D5D6D7D8D9DADBDCDDDEDFE0E1E2E3E4E5E6E7E8E9EAEBECEDEEEF
F0F1F2F3F4F5F6F7F8F9FAFBFCFDFEFF
>
<
1415161718191A1B1C1D1E1F1F202122232425262728292A2A2B2C2D2E2F30313233343536363738
393A3B3C3D3E3F40414142434445464748494A4B4C4D4D4E4F50515253545556575858595A5B5C5D
5E5F60616263646465666768696A6B6C6D6E6F6F707172737475767778797A7B7B7C7D7E7F808182
83848586868788898A8B8C8D8E8F90919292939495969798999A9B9C9D9D9E9FA0A1A2A3A4A5A6A7
A8A9A9AAABACADAEAFB0B1B2B3B4B4B5B6B7B8B9BABBBCBDBEBFC0C0C1C2C3C4C5C6C7C8C9CACBCB
CCCDCECFD0D1D2D3D4D5D6D7D7D8D9DADBDCDDDEDFE0E1E2E2E3E4E5E6E7E8E9EAEBECEDEEEEEFF0
F1F2F3F4F5F6F7F8F9F9FAFBFCFDFEFF
>
<
ABAAAAA9A8A7A7A6A5A5A4A3A3A2A1A1A09F9F9E9D9D9C9B9B9A9999989797969595949393929191
908F8F8E8D8D8C8B8B8A8989888787868585848383828181807F7F7E7D7D7C7B7B7A797978777776
7575747373727171706F6F6E6D6D6C6B6B6A6969686767666565646362626160605F5E5E5D5C5C5B
5A5A5958585756565554545352525150504F4E4E4D4C4C4B4A4A4948484746464544444342424140
403F3E3E3D3C3C3B3A3A3938383736363534343332323130302F2E2E2D2C2C2B2A2A292828272626
25242423222121201F1F1E1D1D1C1B1B1A1919181717161515141313121111100F0F0E0D0D0C0B0B
0A090908070706050504030302010100
>
0
1 %_Br
[
0 0.08 0.67 0 1 50 14 %_Bs
1 1 0 0 1 50 100 %_Bs
BD
%AI5_EndGradient
%AI5_End_NonPrinting--
%AI5_BeginPalette
144 161 Pb
Pn
Pc
1 g
Pc
0 g
Pc
0 0 0 0 k
Pc
0.75 g
Pc
0.5 g
Pc
0.25 g
Pc
0 g
Pc
Bb
2 (Black & White) -4014 4716 0 0 1 0 0 1 0 0 Bg
0 BB
Pc
0.25 0 0 0 k
Pc
0.5 0 0 0 k
Pc
0.75 0 0 0 k
Pc
1 0 0 0 k
Pc
0.25 0.25 0 0 k
Pc
0.5 0.5 0 0 k
Pc
0.75 0.75 0 0 k
Pc
1 1 0 0 k
Pc
Bb
2 (Red & Yellow) -4014 4716 0 0 1 0 0 1 0 0 Bg
0 BB
Pc
0 0.25 0 0 k
Pc
0 0.5 0 0 k
Pc
0 0.75 0 0 k
Pc
0 1 0 0 k
Pc
0 0.25 0.25 0 k
Pc
0 0.5 0.5 0 k
Pc
0 0.75 0.75 0 k
Pc
0 1 1 0 k
Pc
Bb
0 0 0 0 Bh
2 (Yellow & Blue Radial) -4014 4716 0 0 1 0 0 1 0 0 Bg
0 BB
Pc
0 0 0.25 0 k
Pc
0 0 0.5 0 k
Pc
0 0 0.75 0 k
Pc
0 0 1 0 k
Pc
0.25 0 0.25 0 k
Pc
0.5 0 0.5 0 k
Pc
0.75 0 0.75 0 k
Pc
1 0 1 0 k
Pc
(Yellow Stripe) 0 0 1 1 0 0 0 0 0 [1 0 0 1 0 0] p
Pc
0.25 0.125 0 0 k
Pc
0.5 0.25 0 0 k
Pc
0.75 0.375 0 0 k
Pc
1 0.5 0 0 k
Pc
0.125 0.25 0 0 k
Pc
0.25 0.5 0 0 k
Pc
0.375 0.75 0 0 k
Pc
0.5 1 0 0 k
Pc
0.375 0.375 0.75 0 k
Pc
0 0.25 0.125 0 k
Pc
0 0.5 0.25 0 k
Pc
0 0.75 0.375 0 k
Pc
0 1 0.5 0 k
Pc
0 0.125 0.25 0 k
Pc
0 0.25 0.5 0 k
Pc
0 0.375 0.75 0 k
Pc
0 0.5 1 0 k
Pc
0 0.79 0.91 0 (PANTONE Warm Red CV) 0 x
Pc
0.125 0 0.25 0 k
Pc
0.25 0 0.5 0 k
Pc
0.375 0 0.75 0 k
Pc
0.5 0 1 0 k
Pc
0.25 0 0.125 0 k
Pc
0.5 0 0.25 0 k
Pc
0.75 0 0.375 0 k
Pc
1 0 0.5 0 k
Pc
0.5 1 0 0 k
Pc
0.25 0.125 0.125 0 k
Pc
0.5 0.25 0.25 0 k
Pc
0.75 0.375 0.375 0 k
Pc
1 0.5 0.5 0 k
Pc
0.25 0.25 0.125 0 k
Pc
0.5 0.5 0.25 0 k
Pc
0.75 0.75 0.375 0 k
Pc
1 1 0.5 0 k
Pc
0 1 0.5 0 k
Pc
0.125 0.25 0.125 0 k
Pc
0.25 0.5 0.25 0 k
Pc
0.375 0.75 0.375 0 k
Pc
0.5 1 0.5 0 k
Pc
0.125 0.25 0.25 0 k
Pc
0.25 0.5 0.5 0 k
Pc
0.375 0.75 0.75 0 k
Pc
0.5 1 1 0 k
Pc
0.75 0.75 0.375 0 k
Pc
0.125 0.125 0.25 0 k
Pc
0.25 0.25 0.5 0 k
Pc
0.375 0.375 0.75 0 k
Pc
0.5 0.5 1 0 k
Pc
0.25 0.125 0.25 0 k
Pc
0.5 0.25 0.5 0 k
Pc
0.75 0.375 0.75 0 k
Pc
1 0.5 1 0 k
Pc
0 0.79 0.91 0 (PANTONE Warm Red CV) 0 x
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
1 0.5 0.5 0 k
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
0 0.25 1 0 (Orange Yellow) 0 x
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
0 1 0.5 0 k
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
1 0 0.5 0 k
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
0 0.45 1 0 (Orange) 0 x
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
0.375 0.375 0.75 0 k
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
0 0.79 0.91 0 (PANTONE Warm Red CV) 0 x
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
1 0.65 0 0 k
Pc
0 0 0 0 k
Pc
Pc
Pc
Pc
Pc
Pc
Pc
Pc
0 0 1 0 k
Pc
PB
%AI5_EndPalette
%%EndSetup
%AI5_BeginLayer
1 1 1 1 0 0 0 79 128 255 Lb
(Layer 1) Ln
0 A
1 Ap
0 O
1 0.65 0 0 k
800 Ar
0 J 0 j 1 w 4 M []0 d
%AI3_Note:
0 D
285.0121 311.7976 m
357.5043 302.5199 L
361.6071 392.7105 L
376.3322 474.1377 L
342.6527 475.6628 L
327.6333 483.4165 L
258.8269 486.3189 L
254.4361 405.0427 L
242.0523 312.2099 L
285.0121 311.7976 L
f
0 0.79 0.91 0 k
1.25 w
295.4466 337.6172 m
368.4943 335.3343 L
363.9288 425.5026 L
370.7771 507.9667 L
337.1066 506.2547 L
321.4128 512.5323 L
252.6452 508.8228 L
256.0692 427.5002 L
252.6452 333.9077 L
295.4466 337.6172 L
f
u
0 Ap
1 0.65 0 0 k
1 w
320.532 390.6149 m
312.9017 388.534 l
317.0637 398.5921 l
321.2256 426.6854 l
316.0232 427.7258 l
322.2662 436.3965 l
330.0436 465.6249 l
316.3701 462.7557 l
323.5798 475.9563 331.2311 484.5534 v
321.2256 492.2363 l
288.9913 478.0373 297.6622 431.9088 v
290.9988 433.0755 l
297.3888 384.7188 l
291.9867 383.3315 l
297.5214 372.0383 305.2714 366.6837 v
305.9749 366.1976 295.5601 404.4882 306.6587 442.6395 c
307.6992 440.2117 l
298.855 399.5459 307.6992 366.6837 v
308.1064 365.9033 312.5286 366.4235 v
320.532 381.5106 320.532 390.6149 v
f
u
*u
1 g
263.6948 355.9856 m
265.2612 355.9856 L
265.2612 359.2513 L
265.9515 359.2513 266.6153 359.2513 267.2791 359.3575 c
267.2791 355.9856 L
269.6155 355.9856 L
269.6155 355.3749 L
267.2791 355.3749 L
267.2791 347.2505 L
267.2791 346.7726 267.2791 346.0558 268.288 346.0558 c
268.9783 346.0558 269.35 346.5337 269.7748 347.0381 c
270.1996 346.7461 L
269.6951 345.7372 268.3942 345.1265 267.3322 345.1265 c
265.4205 345.1265 265.2081 346.162 265.2081 347.4364 c
265.2081 355.3749 L
263.6948 355.3749 L
263.6948 355.9856 l
f
*U
*u
285.7796 348.7639 m
285.1689 346.8788 284.1069 345.2327 281.3457 345.1265 c
277.2304 345.1265 275.9825 348.5515 275.9825 350.3835 c
275.9825 355.1094 279.7792 356.2511 281.2926 356.2511 c
283.0184 356.2511 285.461 355.4546 285.461 353.4102 c
285.461 352.6934 285.0096 352.003 284.2662 352.003 c
283.5494 352.003 283.0184 352.481 283.0184 353.2509 c
283.0184 354.2864 283.868 354.4191 283.868 354.7112 c
283.868 355.428 282.1953 355.7201 281.6112 355.7201 c
279.0624 355.7201 278.3986 353.8616 278.3986 350.3835 c
278.3986 348.7905 278.7969 347.5691 278.9562 347.1974 c
279.3544 346.3213 280.1775 345.7637 281.5581 345.6841 c
283.098 345.6044 284.5848 346.8523 285.222 348.7639 C
285.7796 348.7639 l
f
*U
*u
291.9344 345.4717 m
291.9344 346.0823 L
293.9788 346.0823 L
293.9788 363.1542 L
291.9344 363.1542 L
291.9344 363.7648 L
293.0761 363.7648 L
294.0585 363.7648 295.0939 363.8179 296.0497 364.0038 c
296.0497 346.0823 L
298.0941 346.0823 L
298.0941 345.4717 L
291.9344 345.4717 l
f
*U
u
310.0634 446.075 m
305.3828 425.2059 306.7298 391.3708 v
307.1338 381.222 308.2436 371.8929 309.5993 363.8029 C
309.6066 363.8025 L
310.4883 356.6987 311.0781 354.1272 313.3768 345.5676 C
313.2426 340.0473 L
294.8367 398.8155 310.0634 446.075 V
f
321.3622 464.1699 m
325.5016 466.2317 331.4359 466.9819 v
337.9224 455.0924 321.9584 434.793 v
331.4821 456.0522 329.2358 462.7122 v
326.7243 464.2727 321.3622 464.1699 v
f
319.4002 428.4819 m
323.1177 427.6214 324.9024 429.0668 v
321.386 415.3445 322.3077 407.7964 v
323.2297 400.2483 316.5788 395.4159 y
322.2441 402.584 320.4635 408.4226 v
319.2289 412.4694 320.6101 422.8271 322.1681 426.1155 c
320.7131 426.3196 319.4002 428.4819 v
f
315.7246 392.3281 m
321.8677 393.0631 322.5131 396.1662 v
323.265 377.6058 314.7299 369.9571 v
321.2425 380.1152 320.2206 390.6235 v
315.7246 392.3281 l
f
298.4445 384.6023 m
296.4635 382.3836 290.5192 387.2778 v
292.4131 374.803 304.1781 369.0924 v
296.0814 375.1928 293.9 381.7824 v
296.7611 382.6245 298.4445 384.6023 v
f
296.5483 389.3335 m
288.5102 409.7356 290.2325 437.3036 v
292.1098 432.3112 298.1424 430.5604 v
295.3003 429.9794 293.6387 430.2313 v
289.4335 418.5932 296.5483 389.3335 v
f
330.3126 484.1353 m
327.3003 506.2722 308.4549 483.8853 v
293.4491 466.0592 295.2373 450.9247 296.1578 442.4811 c
296.3932 440.3206 293.366 465.0316 309.8067 481.2933 c
326.2471 497.5553 329.9609 485.0794 330.3126 484.1353 c
f
U
0 0 1 0 k
302.5528 503.0164 m
287.7656 507.2395 283.0593 458.227 v
279.4282 473.3549 288.8204 494.7509 v
298.2122 516.1468 302.5528 503.0164 y
f
284.2076 506.5994 m
276.6655 495.2557 278.3767 483.1729 v
272.6565 505.9183 284.2076 506.5994 v
f
339.7135 474.7902 m
348.6321 478.0799 335.8615 444.8834 v
342.4718 454.5848 346.6326 469.8253 v
349.303 479.6062 339.7135 474.7902 y
f
354.1382 477.3767 m
360.4435 471.669 355.9752 464.1187 v
367.1908 475.904 354.1382 477.3767 v
f
U
U
*u
1 g
258.2029 317.4593 m
256.6821 317.4593 L
256.6821 325.2598 L
258.7512 325.2598 L
260.3858 325.2598 261.4514 324.608 261.4514 322.839 c
261.4514 321.1837 260.5513 320.3767 258.9581 320.3767 c
258.2029 320.3767 L
258.2029 317.4593 l
f
1 D
258.2029 321.6389 m
258.5132 321.6389 L
259.4133 321.6389 259.8995 321.8354 259.8995 322.8493 c
259.8995 323.8528 259.3202 323.9976 258.4719 323.9976 c
258.2029 323.9976 L
258.2029 321.6389 l
f
*U
*u
0 D
269.0694 321.3699 m
269.0694 323.5528 270.6523 325.4667 272.9283 325.4667 c
275.2043 325.4667 276.7871 323.5528 276.7871 321.3699 c
276.7871 319.1353 275.2043 317.2524 272.9283 317.2524 c
270.6523 317.2524 269.0694 319.1353 269.0694 321.3699 c
f
1 D
270.6419 321.432 m
270.6419 320.2526 271.6351 318.7525 272.9283 318.7525 c
274.2215 318.7525 275.2146 320.2526 275.2146 321.432 c
275.2146 322.6941 274.2628 323.9666 272.9283 323.9666 c
271.5937 323.9666 270.6419 322.6941 270.6419 321.432 c
f
*U
*u
0 D
287.2943 319.9422 m
287.315 319.9422 L
288.8668 325.3632 L
289.7668 325.3632 L
291.3807 319.9422 L
291.4014 319.9422 L
292.9326 325.2598 L
294.5258 325.2598 L
291.8877 317.3041 L
290.7704 317.3041 L
289.2185 322.4044 L
289.1978 322.4044 L
287.7288 317.3041 L
286.6115 317.3041 L
284.1286 325.2598 L
285.7218 325.2598 L
287.2943 319.9422 l
f
*U
*u
303.7595 323.9356 m
303.7595 322.2182 L
306.1803 322.2182 L
306.1803 320.894 L
303.7595 320.894 L
303.7595 318.7835 L
306.2734 318.7835 L
306.2734 317.4593 L
302.2387 317.4593 L
302.2387 325.2598 L
306.2734 325.2598 L
306.2734 323.9356 L
303.7595 323.9356 l
f
*U
*u
319.8602 317.4593 m
318.0187 317.4593 L
316.1255 320.6043 L
316.1048 320.6043 L
316.1048 317.4593 L
314.5841 317.4593 L
314.5841 325.2598 L
316.6428 325.2598 L
318.1843 325.2598 319.2499 324.577 319.2499 322.9114 c
319.2499 321.9182 318.7015 320.925 317.6567 320.7492 C
319.8602 317.4593 l
f
1 D
316.1048 321.6699 m
316.3014 321.6699 L
317.1394 321.6699 317.7291 321.9182 317.7291 322.87 c
317.7291 323.8321 317.1187 324.0183 316.3117 324.0183 c
316.1048 324.0183 L
316.1048 321.6699 l
f
*U
*u
0 D
329.1754 323.9356 m
329.1754 322.2182 L
331.5962 322.2182 L
331.5962 320.894 L
329.1754 320.894 L
329.1754 318.7835 L
331.6894 318.7835 L
331.6894 317.4593 L
327.6546 317.4593 L
327.6546 325.2598 L
331.6894 325.2598 L
331.6894 323.9356 L
329.1754 323.9356 l
f
*U
*u
340 325.2598 m
342.1725 325.2598 L
344.4279 325.2598 345.9383 323.5735 345.9383 321.3492 c
345.9383 319.156 344.3865 317.4593 342.1622 317.4593 c
340 317.4593 L
340 325.2598 l
f
1 D
341.5208 318.7835 m
341.7691 318.7835 L
343.6416 318.7835 344.3658 319.8181 344.3658 321.3596 c
344.3658 323.0562 343.4968 323.9356 341.7691 323.9356 c
341.5208 323.9356 L
341.5208 318.7835 l
f
*U
LB
%AI5_EndLayer--
%%PageTrailer
gsave annotatepage grestore showpage
%%Trailer
Adobe_IllustratorA_AI5 /terminate get exec
Adobe_level2_AI5 /terminate get exec
%%EOF
