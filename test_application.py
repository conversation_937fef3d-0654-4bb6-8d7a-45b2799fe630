#!/usr/bin/env python3
"""
Test script for the YOLO Object Detection Application

This script demonstrates the functionality of the main.py application
by running various test cases and scenarios.
"""

import os
import subprocess
import sys
from pathlib import Path


def run_command(command, description):
    """Run a command and display results."""
    print(f"\n{'='*60}")
    print(f"TEST: {description}")
    print(f"COMMAND: {command}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        print(f"RETURN CODE: {result.returncode}")
        
        return result.returncode == 0
    except Exception as e:
        print(f"ERROR: {e}")
        return False


def main():
    """Run test cases for the YOLO detection application."""
    print("YOLO Object Detection Application - Test Suite")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists("main.py"):
        print("ERROR: main.py not found. Please run this script from the project directory.")
        sys.exit(1)
    
    # Check if model files exist
    models_exist = {
        'fireworks': os.path.exists("fire_smoke.pt"),
        'insects': os.path.exists("bugs.pt")
    }
    
    print(f"Model files status:")
    print(f"  fire_smoke.pt: {'✓' if models_exist['fireworks'] else '✗'}")
    print(f"  bugs.pt: {'✓' if models_exist['insects'] else '✗'}")
    
    # Find test images
    test_images = []
    train_dir = Path("train-林业虫害yolo(1)/train8")
    if train_dir.exists():
        for img_file in train_dir.glob("train_batch*.jpg"):
            test_images.append(str(img_file))
    
    if not test_images:
        print("WARNING: No test images found. Some tests will be skipped.")
    else:
        print(f"Found {len(test_images)} test images")
    
    # Test cases
    test_results = []
    
    # Test 1: Help message
    success = run_command("./venv/bin/python main.py --help", "Display help message")
    test_results.append(("Help message", success))
    
    # Test 2: Invalid detection type
    success = run_command("./venv/bin/python main.py --type invalid --input dummy.jpg", 
                         "Invalid detection type (should fail)")
    test_results.append(("Invalid detection type", not success))  # Should fail
    
    # Test 3: Missing input file
    success = run_command("./venv/bin/python main.py --type fireworks --input nonexistent.jpg", 
                         "Missing input file (should fail)")
    test_results.append(("Missing input file", not success))  # Should fail
    
    # Test 4: Fireworks detection (if model and images available)
    if models_exist['fireworks'] and test_images:
        test_image = test_images[0]
        success = run_command(f"./venv/bin/python main.py --type fireworks --input \"{test_image}\"", 
                             "Fireworks detection")
        test_results.append(("Fireworks detection", success))
        
        # Test 5: Fireworks detection with verbose output
        success = run_command(f"./venv/bin/python main.py --type fireworks --input \"{test_image}\" --verbose", 
                             "Fireworks detection (verbose)")
        test_results.append(("Fireworks detection (verbose)", success))
        
        # Test 6: Fireworks detection with custom confidence
        success = run_command(f"./venv/bin/python main.py --type fireworks --input \"{test_image}\" --confidence 0.5", 
                             "Fireworks detection (custom confidence)")
        test_results.append(("Fireworks detection (custom confidence)", success))
    
    # Test 7: Insects detection (if model available)
    if models_exist['insects'] and test_images:
        test_image = test_images[0]
        success = run_command(f"./venv/bin/python main.py --type insects --input \"{test_image}\"", 
                             "Insects detection")
        test_results.append(("Insects detection", success))
    
    # Test 8: Short options
    if models_exist['fireworks'] and test_images:
        test_image = test_images[0]
        success = run_command(f"./venv/bin/python main.py -t fireworks -i \"{test_image}\" -v", 
                             "Short options test")
        test_results.append(("Short options", success))
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = 0
    total = len(test_results)
    
    for test_name, success in test_results:
        status = "PASS" if success else "FAIL"
        print(f"{test_name:<40} {status}")
        if success:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print(f"⚠️  {total - passed} test(s) failed")
    
    # Check for output files
    print(f"\n{'='*60}")
    print("OUTPUT FILES CHECK")
    print(f"{'='*60}")
    
    output_files = list(Path(".").glob("**/*_detected.jpg"))
    if output_files:
        print(f"Found {len(output_files)} output files:")
        for output_file in output_files:
            print(f"  ✓ {output_file}")
    else:
        print("No output files found")


if __name__ == "__main__":
    main()
